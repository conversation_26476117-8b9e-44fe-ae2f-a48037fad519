# 🚨 Уязвимость неправильного расчета комиссий в burn операциях

## 📋 **Описание проблемы**

Обнаружена архитектурная проблема в последовательности операций функции `burn()`, которая приводит к неправильному начислению максимальных комиссий пользователям при наличии достаточных средств в Fractional Reserve vaults.

## 🔍 **Анализ текущей логики**

### **Последовательность операций в Vault.burn():**

```solidity
function burn(address _asset, uint256 _amountIn, uint256 _minAmountOut, address _receiver, uint256 _deadline) external {
    uint256 fee;
    
    // 1. СНАЧАЛА рассчитываем комиссии (НА ОСНОВЕ НЕПОЛНОЙ ИНФОРМАЦИИ)
    (amountOut, fee) = getBurnAmount(_asset, _amountIn);
    
    // 2. ПОТОМ достаем средства из FR vault
    divest(_asset, amountOut + fee);
    
    // 3. Выполняем операцию
    VaultLogic.burn(/* ... */);
}
```

### **Проблема в _amountOutBeforeFee():**

```solidity
function _amountOutBeforeFee(address _oracle, IMinter.AmountOutParams memory params) internal view {
    // Рассчитывает allocationValue только по totalSupplies (бухгалтерский учет)
    uint256 allocationValue = IVault(address(this)).totalSupplies(params.asset) * assetPrice / assetDecimalsPow;
    
    // Если физически в контракте недостаточно средств:
    if (allocationValue < assetValue || capValue <= assetValue) {
        newRatio = 0;  // ← МАКСИМАЛЬНАЯ КОМИССИЯ!
    }
    
    // НО средства могут быть в FR vault и будут извлечены позже!
}
```

### **Применение максимальных комиссий:**

```solidity
function _applyFeeSlopes(IMinter.FeeData memory fees, IMinter.FeeSlopeParams memory params) internal pure {
    // Для burn операций с newRatio = 0:
    if (params.ratio < fees.optimalRatio) {  // 0 < optimalRatio = true
        if (params.ratio < fees.burnKinkRatio) {  // 0 < burnKinkRatio = true
            uint256 excessRatio = fees.burnKinkRatio - params.ratio;  // = burnKinkRatio
            rate = fees.slope0 + (fees.slope1 * excessRatio / fees.burnKinkRatio);  // ← МАКСИМУМ!
        }
    }
}
```

## 💰 **Экономическое воздействие**

### **Пример сценария:**

```
Состояние протокола:
- totalSupplies[USDT] = 1000 USDT (общий учет)
- Физически в контракте: 100 USDT  
- В FR vault: 900 USDT
- totalBorrows[USDT] = 0 USDT

Пользователь хочет burn 200 cUSD → получить 200 USDT

Текущая логика:
1. allocationValue = 1000 * price
2. assetValue = 200 * price
3. availableBalance() = 100 USDT < 200 USDT
4. newRatio = 0 → МАКСИМАЛЬНАЯ комиссия (например, 5%)
5. fee = 200 * 0.05 = 10 USDT
6. amountOut = 190 USDT
7. divest(190 + 10 = 200) → извлекает 100 USDT из FR vault
8. Пользователь получает 190 USDT вместо ~198 USDT

Результат: Пользователь переплачивает ~8 USDT за "дефицит", которого нет!
```

## 🎯 **Корневая причина**

**`allocationValue` рассчитывается по `totalSupplies` (бухгалтерский учет), но не учитывает реальную доступность средств включая Fractional Reserve vaults.**

### **Что происходит:**

1. **`totalSupplies[asset]`** - общий учет всех средств протокола
2. **Физический баланс контракта** может быть меньше из-за инвестирования в FR vaults
3. **Расчет комиссий** основан на физическом балансе (через availableBalance)
4. **Извлечение средств** из FR происходит ПОСЛЕ расчета комиссий

## 🛠 **Возможные решения**

### **Вариант 1: Изменить порядок операций**

```solidity
function burn(address _asset, uint256 _amountIn, uint256 _minAmountOut, address _receiver, uint256 _deadline) external {
    // 1. СНАЧАЛА обеспечиваем максимальную ликвидность
    divest(_asset, _amountIn); // Достаем потенциально нужные средства
    
    // 2. ПОТОМ рассчитываем справедливые комиссии
    (amountOut, fee) = getBurnAmount(_asset, _amountIn);
    
    // 3. Выполняем операцию
    VaultLogic.burn(/* ... */);
}
```

### **Вариант 2: Модифицировать расчет allocationValue**

```solidity
function _amountOutBeforeFee(address _oracle, IMinter.AmountOutParams memory params) internal view {
    // Учитывать реальную доступность включая FR vaults
    uint256 realAvailableBalance = availableBalance() + getFRVaultBalance(params.asset);
    uint256 allocationValue = realAvailableBalance * assetPrice / assetDecimalsPow;
    
    // Теперь расчет будет справедливым
}
```

### **Вариант 3: Предварительная проверка ликвидности**

```solidity
function getBurnAmount(address _asset, uint256 _amountIn) public view returns (uint256 amountOut, uint256 fee) {
    // Проверяем общую доступность средств (контракт + FR vault)
    uint256 totalAvailable = availableBalance(_asset) + loaned(_asset);
    
    if (totalAvailable >= потребность) {
        // Рассчитываем комиссии как при достаточной ликвидности
    } else {
        // Применяем комиссии за реальный дефицит
    }
}
```

## 🚨 **Severity Assessment**

**MEDIUM** - Потеря средств пользователей через завышенные комиссии при определенных условиях:

- ✅ **Финансовые потери**: Пользователи переплачивают комиссии
- ✅ **Системные условия**: Когда средства инвестированы в FR vaults
- ✅ **Воспроизводимость**: Легко воспроизводится при правильных условиях
- ❌ **Прямая потеря TVL**: Нет прямой потери TVL протокола

## 🔬 **Дополнительные исследования**

### **Вопросы для анализа:**

1. **Как часто возникает ситуация** когда средства в FR vault > средства в контракте?
2. **Какие максимальные комиссии** могут быть применены при newRatio = 0?
3. **Есть ли защитные механизмы** от чрезмерных комиссий?
4. **Влияет ли это на redeem операции** аналогичным образом?

### **Тестовые сценарии:**

```solidity
// Тест 1: Burn при средствах в FR vault
function test_burn_with_FR_vault_funds() public {
    // Setup: большая часть средств в FR vault
    // Action: burn операция
    // Assert: комиссии должны быть справедливыми
}

// Тест 2: Сравнение комиссий до/после divest
function test_fee_comparison_before_after_divest() public {
    // Сравнить комиссии в двух сценариях
}
```

## 📊 **Impact Analysis**

- **Пользователи**: Переплачивают комиссии за несуществующий дефицит
- **Протокол**: Получает незаслуженную прибыль от завышенных комиссий  
- **Конкуренты**: Могут предложить более справедливые условия
- **Репутация**: Риск потери доверия при обнаружении проблемы

---

*Анализ выполнен в рамках аудита протокола CAP. Требуется дополнительное исследование для определения точного экономического воздействия и разработки оптимального решения.*
