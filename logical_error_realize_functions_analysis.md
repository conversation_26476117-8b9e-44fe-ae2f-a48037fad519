# 🚨 КРИТИЧЕСКАЯ ЛОГИЧЕСКАЯ ОШИБКА: Смешение процентов LP и Restaker

## 📋 **Краткое описание проблемы**

Обнаружена **фундаментальная логическая ошибка** в архитектуре протокола CAP: функция `realizeRestakerInterest` влияет на расчеты процентов для LP через переменную `totalDebt`, хотя эти процентные потоки должны быть полностью разделены.

**Severity: CRITICAL** - Нарушение базовой экономической модели протокола

---

## 🔍 **Техническая суть проблемы**

### **Анализ функции maxRealization (для LP):**

```solidity
function maxRealization(ILender.LenderStorage storage $, address _asset)
    internal view returns (uint256 realization)
{
    ILender.ReserveData storage reserve = $.reservesData[_asset];
    uint256 totalDebt = IERC20(reserve.debtToken).totalSupply();  // ← ВКЛЮЧАЕТ RESTAKER ДОЛГ!
    uint256 reserves = IVault(reserve.vault).availableBalance(_asset);
    uint256 vaultDebt = reserve.debt;
    uint256 totalUnrealizedInterest = reserve.totalUnrealizedInterest;

    if (totalDebt > vaultDebt + totalUnrealizedInterest) {
        realization = totalDebt - vaultDebt - totalUnrealizedInterest;  // ← НЕВЕРНЫЙ РАСЧЕТ!
    }
}
```

### **Проблема в realizeRestakerInterest:**

```solidity
function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    // ...
    IDebtToken(reserve.debtToken).mint(_agent, realizedInterest + unrealizedInterest);
    //                                   ↑
    // УВЕЛИЧИВАЕТ totalSupply() = totalDebt для LP расчетов!
}
```

### **Логическая ошибка:**

1. **`totalDebt = IERC20(reserve.debtToken).totalSupply()`** включает:
   - Основной долг агентов (principal)
   - **Долг от restaker процентов** (через mint в realizeRestakerInterest)

2. **Функция `maxRealization` для LP** использует этот `totalDebt` для расчета процентов LP

3. **Результат:** Restaker проценты влияют на LP проценты, что логически неверно!

---

## 💰 **Экономическое воздействие**

### **Неправильное распределение процентов:**

```
Сценарий:
1. Агент занимает 1000 USDT (principal debt)
2. Накапливается 50 USDT restaker процентов
3. realizeRestakerInterest минтит 50 USDT долговых токенов
4. totalDebt = 1050 USDT (principal + restaker interest)

Проблема в maxRealization:
- LP проценты рассчитываются от 1050 USDT
- НО должны рассчитываться только от 1000 USDT principal!
- LP получают проценты с restaker процентов!
```

### **Двойное начисление процентов:**

| Компонент | Должно быть | Фактически | Проблема |
|-----------|-------------|------------|----------|
| Principal debt | 1000 USDT | 1000 USDT | ✅ Корректно |
| Restaker interest | 50 USDT | 50 USDT | ✅ Корректно |
| LP interest base | 1000 USDT | **1050 USDT** | ❌ Включает restaker! |
| LP interest | ~30 USDT | **~31.5 USDT** | ❌ Завышено на 5% |

---

## 🎯 **Векторы эксплуатации**

### **Атака 1: Искусственное завышение LP процентов**

```solidity
// Атакующий может специально увеличивать restaker долг для завышения LP процентов
function inflateLP_Interest(address agent, address asset) external {
    // 1. Накапливаем большой restaker долг
    for (uint i = 0; i < 10000; i++) {
        vm.warp(block.timestamp + 1);
        lender.realizeRestakerInterest(agent, asset);
    }
    
    // 2. totalDebt теперь включает весь restaker долг
    uint256 inflatedTotalDebt = IERC20(debtToken).totalSupply();
    
    // 3. LP проценты рассчитываются от завышенной базы
    uint256 lpInterest = lender.maxRealization(asset);
    // lpInterest теперь больше чем должно быть!
}
```

### **Атака 2: Манипуляция через координированные действия**

```solidity
// Группа агентов координирует действия для максимизации LP процентов
contract CoordinatedAttack {
    function executeCoordinatedInflation() external {
        address[] memory agents = getAllAgents();
        
        // Все агенты одновременно накапливают restaker долг
        for (uint i = 0; i < agents.length; i++) {
            accumulateRestakerDebt(agents[i]);
        }
        
        // Теперь LP проценты рассчитываются от завышенной базы
        // LP получают незаслуженные проценты
    }
}
```

---

## 🧪 **Доказательство концепции**

### **Тест демонстрации логической ошибки:**

```solidity
function test_LP_restaker_interest_confusion() public {
    // 1. Настройка: агент занимает 1000 USDT
    uint256 principalBorrow = 1000 * 1e6;
    lender.borrow(address(usdt), principalBorrow, agent);
    
    uint256 initialTotalDebt = IERC20(debtToken).totalSupply();
    uint256 initialLPInterest = lender.maxRealization(address(usdt));
    
    console.log("Initial totalDebt:", initialTotalDebt / 1e6, "USDT");
    console.log("Initial LP interest:", initialLPInterest / 1e6, "USDT");
    
    // 2. Накапливаем restaker проценты
    vm.warp(block.timestamp + 30 days);
    
    // 3. Реализуем restaker проценты (увеличивает totalDebt)
    uint256 restakerInterest = lender.realizeRestakerInterest(agent, address(usdt));
    
    uint256 finalTotalDebt = IERC20(debtToken).totalSupply();
    uint256 finalLPInterest = lender.maxRealization(address(usdt));
    
    console.log("Final totalDebt:", finalTotalDebt / 1e6, "USDT");
    console.log("Final LP interest:", finalLPInterest / 1e6, "USDT");
    console.log("Restaker interest added:", restakerInterest / 1e6, "USDT");
    
    // 4. Демонстрируем проблему
    uint256 totalDebtIncrease = finalTotalDebt - initialTotalDebt;
    uint256 lpInterestIncrease = finalLPInterest - initialLPInterest;
    
    assertEq(totalDebtIncrease, restakerInterest, "TotalDebt should increase by restaker interest");
    assertGt(lpInterestIncrease, 0, "LP interest should NOT increase due to restaker interest!");
    
    // ❌ ПРОБЛЕМА: LP проценты увеличились из-за restaker процентов!
    console.log("PROBLEM: LP interest increased by:", lpInterestIncrease / 1e6, "USDT");
    console.log("This should be ZERO - LP and restaker interests should be separate!");
}
```

### **Тест сравнения правильной vs неправильной логики:**

```solidity
function test_correct_vs_incorrect_LP_calculation() public {
    // Правильный расчет LP процентов (только от principal)
    uint256 principalDebt = getPrincipalDebt(agent, asset);
    uint256 correctLPInterest = calculateLPInterest(principalDebt);
    
    // Неправильный расчет (текущая реализация)
    uint256 totalDebt = IERC20(debtToken).totalSupply(); // Включает restaker!
    uint256 incorrectLPInterest = lender.maxRealization(asset);
    
    console.log("Correct LP interest (principal only):", correctLPInterest / 1e6, "USDT");
    console.log("Incorrect LP interest (includes restaker):", incorrectLPInterest / 1e6, "USDT");
    
    uint256 overcharge = incorrectLPInterest - correctLPInterest;
    console.log("LP overcharge due to restaker inclusion:", overcharge / 1e6, "USDT");
    
    assertGt(overcharge, 0, "Current implementation overcharges LP interest");
}
```

---

## 🛠 **Критические исправления**

### **1. КРИТИЧЕСКОЕ: Разделение principal и restaker долга**

```solidity
struct ReserveData {
    // ... существующие поля
    uint256 principalDebt;      // ← НОВОЕ: Только основной долг
    uint256 restakerDebt;       // ← НОВОЕ: Только restaker долг
    // uint256 debt; // ← УДАЛИТЬ или переименовать в totalVaultDebt
}

function maxRealization(ILender.LenderStorage storage $, address _asset)
    internal view returns (uint256 realization)
{
    ILender.ReserveData storage reserve = $.reservesData[_asset];
    
    // ИСПРАВЛЕНИЕ: Использовать только principal долг для LP процентов
    uint256 principalDebt = reserve.principalDebt;  // ← НЕ totalSupply()!
    uint256 reserves = IVault(reserve.vault).availableBalance(_asset);
    uint256 vaultDebt = reserve.debt;
    
    if (principalDebt > vaultDebt) {
        realization = principalDebt - vaultDebt;
    }
}
```

### **2. КРИТИЧЕСКОЕ: Исправление функции borrow**

```solidity
function borrow(...) external returns (uint256 borrowed) {
    // ...
    borrowed = params.amount;
    
    IVault(reserve.vault).borrow(params.asset, borrowed, params.receiver);
    IDebtToken(reserve.debtToken).mint(params.agent, borrowed);
    
    // ИСПРАВЛЕНИЕ: Увеличиваем только principal долг
    reserve.principalDebt += borrowed;  // ← НЕ reserve.debt!
    
    emit Borrow(params.asset, params.agent, borrowed);
}
```

### **3. КРИТИЧЕСКОЕ: Исправление realizeRestakerInterest**

```solidity
function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    // ...
    if (realizedInterest == 0 && unrealizedInterest == 0) return 0;
    
    // ИСПРАВЛЕНИЕ: Разделяем учет realized и unrealized
    if (realizedInterest > 0) {
        reserve.debt += realizedInterest;  // Для vault операций
        IVault(reserve.vault).borrow(_asset, realizedInterest, $.delegation);
    }
    
    if (unrealizedInterest > 0) {
        reserve.restakerDebt += unrealizedInterest;  // ← ОТДЕЛЬНЫЙ УЧЕТ!
        reserve.unrealizedInterest[_agent] += unrealizedInterest;
        reserve.totalUnrealizedInterest += unrealizedInterest;
    }
    
    // Минтить токены только на realized часть
    if (realizedInterest > 0) {
        IDebtToken(reserve.debtToken).mint(_agent, realizedInterest);
    }
}
```

### **4. ВЫСОКИЙ: Новые view функции для корректного учета**

```solidity
function principalDebt(address _asset) external view returns (uint256) {
    return getLenderStorage().reservesData[_asset].principalDebt;
}

function restakerDebt(address _asset) external view returns (uint256) {
    return getLenderStorage().reservesData[_asset].restakerDebt;
}

function totalDebt(address _asset) external view returns (uint256) {
    ILender.ReserveData storage reserve = getLenderStorage().reservesData[_asset];
    return reserve.principalDebt + reserve.restakerDebt;
}
```

---

## 🚨 **Оценка критичности**

### **Severity: CRITICAL**

**Обоснование:**
- ✅ **Фундаментальная ошибка** в экономической модели
- ✅ **Неправильное распределение** процентов между участниками
- ✅ **Системное воздействие** на весь протокол
- ✅ **Нарушение принципов** разделения процентных потоков

### **Системные риски:**
- **Незаслуженные выплаты** LP за счет restaker активности
- **Нарушение экономических** стимулов протокола
- **Потенциальные споры** между участниками
- **Репутационные риски** для протокола

---

## 🎯 **Заключение**

Обнаружена **критическая архитектурная ошибка** в протоколе CAP:

### **Проблема:**
```solidity
// LP проценты рассчитываются от totalDebt, который включает restaker долг
uint256 totalDebt = IERC20(reserve.debtToken).totalSupply();  // ← НЕВЕРНО!
realization = totalDebt - vaultDebt - totalUnrealizedInterest;
```

### **Последствия:**
- **LP получают проценты** с restaker активности
- **Двойное начисление** процентов на одни и те же средства
- **Нарушение экономической** справедливости протокола

### **Решение:**
Полное **разделение учета** principal и restaker долга с отдельными расчетами процентов для каждого типа участников.

**Требуется ЭКСТРЕННОЕ исправление** - ошибка нарушает фундаментальные принципы работы протокола.

---

*Анализ выполнен в рамках аудита протокола CAP. Уязвимость классифицирована как CRITICAL severity из-за нарушения базовой экономической модели и неправильного распределения процентов между участниками.*
