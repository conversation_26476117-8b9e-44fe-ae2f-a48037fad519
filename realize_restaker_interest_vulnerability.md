# 🚨 Уязвимость частых вызовов в `realizeRestakerInterest`

## 📋 **Краткое резюме**

После тщательного анализа функции `realizeRestakerInterest` в контракте BorrowLogic.sol протокола CAP, обнаружена **критическая уязвимость**, которая позволяет атакующим эксплуатировать ошибки округления через частые вызовы функции, что приводит к потере накопленных процентов для рестейкеров.

## 🔍 **Анализ проблемной формулы**

### **Критическая строка кода:**
```solidity
accruedInterest = totalDebt * rate * elapsedTime / (1e27 * SECONDS_IN_YEAR);
```

### **Математическая проблема:**

**Знаменатель огромный:**
- `1e27 = 1,000,000,000,000,000,000,000,000,000` (ray точность)
- `SECONDS_IN_YEAR = 31,536,000` секунд в году
- **Итого знаменатель = `31,536,000,000,000,000,000,000,000,000,000,000`**

**При малых значениях числителя:**
```
Пример атаки:
- totalDebt = 100 USDT (100,000,000 wei)
- rate = 1% = 0.01e27 = 10,000,000,000,000,000,000,000,000
- elapsedTime = 1 секунда

Числитель = 100,000,000 * 10,000,000,000,000,000,000,000,000 * 1
          = 1,000,000,000,000,000,000,000,000,000,000,000

Результат = 1,000,000,000,000,000,000,000,000,000,000,000 / 31,536,000,000,000,000,000,000,000,000,000,000
          = 0.0000317... → ОКРУГЛЯЕТСЯ ДО 0! 🚨
```

## 🚨 **Суть уязвимости:**

1. **Атакующий вызывает функцию каждую секунду**
2. **Каждый раз `elapsedTime = 1` секунда**
3. **Формула возвращает 0 из-за округления**
4. **`lastRealizationTime` сбрасывается**
5. **Проценты теряются навсегда!**

## 🔍 **Анализ контроля доступа**

### **Текущая реализация:**
```solidity
function realizeRestakerInterest(ILender.LenderStorage storage $, address _agent, address _asset)
    public  // ← НЕТ ОГРАНИЧЕНИЙ ДОСТУПА!
    returns (uint256 realizedInterest)
{
    // Любой может вызвать эту функцию для любого агента
}
```

### **Выводы:**
- ❌ **Нет контроля доступа** - любой адрес может вызвать функцию
- ❌ **Нет временных ограничений** - можно вызывать каждый блок
- ❌ **Нет минимальных порогов** - обрабатывает даже нулевые суммы
- ❌ **Нет ограничения частоты** - неограниченные вызовы за блок/период времени

## 📊 **Конкретные примеры атак**

### **Пример 1: Критический порог**
```
Сценарий:
- totalDebt = 1 USDT (1,000,000 wei)
- rate = 0.1% годовых = 0.001e27
- elapsedTime = 1 секунда

Расчет:
числитель = 1,000,000 * 1,000,000,000,000,000,000,000,000 * 1
         = 1,000,000,000,000,000,000,000,000,000,000

результат = 1,000,000,000,000,000,000,000,000,000,000 / 31,536,000,000,000,000,000,000,000,000,000,000
         ≈ 0.0000317 → ОКРУГЛЯЕТСЯ ДО 0! 🚨
```

### **Пример 2: Малый долг, короткий интервал**
```
Сценарий:
- totalDebt = 1,000 USDT (1,000,000,000 wei)
- rate = 5% годовых = 0.05e27
- elapsedTime = 12 секунд (1 блок в Ethereum)

Результат ≈ 19,025 wei ≈ 0.000019 USDT
✅ Это НЕ округлится до нуля (едва выше порога)
```

## 💰 **Пример потерь:**

```
Нормальный сценарий (1000 секунд накопления):
accruedInterest = 100,000,000 * 0.01e27 * 1000 / (1e27 * 31,536,000)
                = 3,170 wei ≈ 0.00317 USDT

Атака (1000 вызовов по 1 секунде):
Каждый вызов: accruedInterest = 0
Общие потери: 3,170 wei за каждые 1000 секунд
Годовые потери: ~100 USDT для долга в 100 USDT
```

## 🎯 **Демонстрация вектора атаки**

### **Успешный сценарий атаки:**
```solidity
// Стратегия атакующего:
for (uint i = 0; i < 1000; i++) {
    // Вызывать каждый блок в течение 1000 блоков
    lender.realizeRestakerInterest(victim_agent, asset);
    // Каждый вызов обрабатывает ~1 секунду процентов
    // Малые суммы округляются до нуля
    // Таймер сбрасывается каждый раз
}

// Результат: 1000 секунд процентов потеряно из-за округления!
```

### **Расчет воздействия:**
```
Нормальный сценарий (1000 секунд накопления):
- elapsedTime = 1000 секунд
- accruedInterest = 1,000,000 * 0.001e27 * 1000 / (1e27 * 31,536,000)
                  = 31,709 wei ≈ 0.000032 USDT

Сценарий атаки (1000 вызовов по 1 секунде каждый):
- Каждый вызов: accruedInterest = 0 (округляется вниз)
- Общие потери: 31,709 wei за 1000 секунд
- Годовые потери: ~1,000 USDT для позиции долга в 1,000,000 USDT
```

## 🚨 **Оценка воздействия уязвимости**

### **Серьезность: ВЫСОКАЯ**

**Воздействие:**
- ✅ **Прямые финансовые потери** рестейкеров
- ✅ **Манипуляция механиками протокола**
- ✅ **Несправедливое преимущество** атакующих
- ✅ **Системный риск** при применении к множественным агентам

**Затронутые стороны:**
- **Рестейкеры**: Теряют накопленные процентные вознаграждения
- **Протокол**: Ущерб репутации, экономическая неэффективность
- **Агенты**: Косвенное воздействие через недовольство рестейкеров

## 🛠 **Рекомендуемые меры защиты**

### **Решение 1: Минимальный временной интервал**
```solidity
uint256 constant MIN_REALIZATION_INTERVAL = 1 hours;

function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    require(
        block.timestamp - reserve.lastRealizationTime[_agent] >= MIN_REALIZATION_INTERVAL,
        "Слишком частая реализация"
    );
    // ... остальная часть функции
}
```

### **Решение 2: Минимальный порог накопления**
```solidity
uint256 constant MIN_ACCRUAL_THRESHOLD = 1000; // Минимум 1000 wei

function realizeRestakerInterest(...) public returns (uint256 realizedInterest) {
    uint256 accruedInterest = ViewLogic.accruedRestakerInterest($, _agent, _asset);
    
    if (accruedInterest < MIN_ACCRUAL_THRESHOLD) {
        return 0; // Не сбрасывать таймер, пусть проценты накапливаются
    }
    
    // Сбрасывать таймер только если обработана значимая сумма
    reserve.lastRealizationTime[_agent] = block.timestamp;
    // ... остальная часть функции
}
```

### **Решение 3: Контроль доступа**
```solidity
modifier onlyAuthorizedRealizer(address _agent) {
    require(
        msg.sender == _agent || 
        msg.sender == delegation || 
        hasRole(REALIZER_ROLE, msg.sender),
        "Неавторизованный реализатор"
    );
    _;
}
```

### **Решение 4: Повышенная точность**
```solidity
function accruedRestakerInterest(...) public view returns (uint256 accruedInterest) {
    uint256 numerator = totalDebt * rate * elapsedTime;
    uint256 denominator = 1e27 * SECONDS_IN_YEAR;
    
    // Проверить, будет ли результат нулевым перед делением
    if (numerator < denominator) {
        return 0; // Явный возврат нуля
    }
    
    accruedInterest = numerator / denominator;
}
```

## 📋 **Рекомендации по тестированию**

### **Тестовые случаи для реализации:**
```solidity
function test_frequent_realization_attack() public {
    // Настроить малую позицию долга
    // Вызывать realizeRestakerInterest каждый блок в течение 100 блоков
    // Проверить общие проценты, потерянные из-за округления
}

function test_minimum_interval_protection() public {
    // Проверить принуждение минимального интервала
    // Тестировать откат при слишком частых вызовах
}

function test_accumulation_vs_frequent_calls() public {
    // Сравнить: 1 вызов через 1000 секунд против 1000 вызовов по 1 секунде каждый
    // Проверить, что накопленные проценты сохраняются
}
```

## 🎯 **Заключение**

Функция `realizeRestakerInterest` содержит **критическую уязвимость**, которая позволяет атакующим эксплуатировать ошибки округления через частые вызовы, что приводит к измеримым финансовым потерям для рестейкеров. 

**Эта одна строка кода - источник критической уязвимости:**
```solidity
accruedInterest = totalDebt * rate * elapsedTime / (1e27 * SECONDS_IN_YEAR);
```

Проблема в том, что при делении на огромный знаменатель малые значения округляются до нуля, но таймер все равно сбрасывается, что позволяет "красть" проценты рестейкеров через частые вызовы функции.

**Требуются немедленные действия** для внедрения защитных механизмов до развертывания протокола или через экстренное обновление, если уже развернут.

---

*Анализ выполнен в рамках аудита протокола CAP. Уязвимость классифицирована как HIGH severity из-за прямых финансовых потерь пользователей.*
